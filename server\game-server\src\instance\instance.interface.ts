export interface Instance {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'stopping' | 'stopped' | 'error';
  host: string;
  port: number;
  gameServerPort?: number;
  maxPlayers: number;
  currentPlayers: number;
  gameMode?: string;
  mapName?: string;
  region?: string;
  version?: string;
  createdAt: Date;
  updatedAt: Date;
  lastHeartbeat?: Date;
  metadata?: Record<string, any>;
}

export interface InstanceConfig {
  maxPlayers: number;
  gameMode?: string;
  mapName?: string;
  region?: string;
  version?: string;
  metadata?: Record<string, any>;
}

export interface InstanceStats {
  cpuUsage: number;
  memoryUsage: number;
  networkIn: number;
  networkOut: number;
  playerCount: number;
  uptime: number;
}

export interface LoadBalancingStrategy {
  name: string;
  selectInstance(instances: Instance[], criteria?: any): Instance | null;
}

export interface MigrationRequest {
  sourceInstanceId: string;
  targetInstanceId: string;
  playerIds?: string[];
  preserveState?: boolean;
  timeout?: number;
}
